# 👻 PhantomEdge Proxy

**下一代反检测代理系统** - 革命性的隐蔽代理解决方案

## ✨ 核心创新特性

### 🔮 幽灵伪装引擎
- **完美克隆**: 100%模拟真实网站（GitHub、企业官网等）
- **动态内容**: 实时生成和更新网站内容
- **真实资源**: 加载真实的外部资源和CDN
- **SEO完整**: 包含完整的SEO元素和分析代码
- **智能选择**: 基于访问者特征自动选择最佳伪装

### ⚡ 量子通信协议
- **自定义加密**: 完全替代传统VLESS协议
- **多层混淆**: 数据包级别的深度混淆
- **动态密钥**: 自动密钥轮换和会话管理
- **时序抖动**: 随机化通信时序避免模式识别
- **零特征**: 完全无法被传统DPI检测

### 🤖 AI驱动流量伪装
- **行为模拟**: 机器学习模拟真实用户行为
- **智能延迟**: 自适应请求间隔和思考时间
- **模式学习**: 持续学习和优化伪装策略
- **负载均衡**: AI驱动的智能IP选择和负载分配
- **异常检测**: 自动识别和规避检测威胁

### 🌊 动态路径系统
- **量子生成**: 基于量子随机数的路径生成
- **无规律性**: 每次访问使用完全不同的路径
- **时间窗口**: 路径自动过期和更新机制
- **用户指纹**: 基于用户特征的个性化路径
- **防重放**: 防止路径重放攻击

### 👻 零痕迹运行
- **无日志模式**: 完全不记录任何操作日志
- **内存运行**: 所有数据仅在内存中处理
- **自动清理**: 定时清理临时数据和缓存
- **反取证**: 无法通过技术手段追踪使用痕迹
- **隐蔽通信**: 通信完全隐藏在正常网站流量中

## 🚀 快速部署

### 1. 环境准备
- Cloudflare账户
- 域名（可选，支持workers.dev子域名）

### 2. 一键部署

#### 方法一：Cloudflare Dashboard部署
1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com)
2. 进入 `Workers & Pages` → `Create application` → `Create Worker`
3. 将 `_worker.js` 内容完整复制到编辑器
4. 点击 `Save and Deploy`

#### 方法二：Wrangler CLI部署
```bash
# 安装Wrangler
npm install -g wrangler

# 登录Cloudflare
wrangler login

# 克隆项目
git clone <repository-url>
cd phantom-edge-proxy

# 部署
wrangler deploy
```

### 3. 环境变量配置

在Cloudflare Workers设置中添加以下环境变量：

#### 🔴 必需变量
| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `PHANTOM_ID` | 系统唯一标识符 | `a1b2c3d4e5f6g7h8` |

#### 🟡 可选变量
| 变量名 | 说明 | 默认值 | 可选值 |
|--------|------|--------|--------|
| `GHOST_MODE` | 伪装模式 | `adaptive` | `adaptive`, `github`, `corporate`, `techblog` |
| `QUANTUM_ENCRYPTION` | 加密算法 | `AES-256-GCM` | `AES-256-GCM`, `ChaCha20-Poly1305` |
| `AI_BEHAVIOR` | AI行为模型 | `human` | `human`, `crawler`, `api` |
| `DYNAMIC_COMPLEXITY` | 路径复杂度 | `high` | `low`, `medium`, `high` |

### 4. 域名绑定（推荐）

1. 在Workers设置中点击 `Triggers`
2. 点击 `Add Custom Domain`
3. 输入域名（需在Cloudflare托管）
4. 等待SSL证书生成

## 📱 客户端配置

### 获取订阅链接

部署完成后，使用以下格式获取订阅：

```
# 通用订阅
https://your-domain.com/sub/your-phantom-id

# Clash订阅
https://your-domain.com/sub/your-phantom-id?clash

# V2Ray订阅
https://your-domain.com/sub/your-phantom-id?v2ray

# Sing-Box订阅
https://your-domain.com/sub/your-phantom-id?sing-box
```

### 支持的客户端

| 客户端 | 平台 | 订阅格式 | 兼容性 |
|--------|------|----------|--------|
| **Clash Verge** | Windows/Mac/Linux | Clash | ✅ 完美支持 |
| **Clash Meta** | 全平台 | Clash | ✅ 完美支持 |
| **V2RayN** | Windows | V2Ray | ✅ 完美支持 |
| **V2RayNG** | Android | V2Ray | ✅ 完美支持 |
| **Sing-Box** | 全平台 | Sing-Box | ✅ 完美支持 |
| **Shadowrocket** | iOS | V2Ray | ✅ 完美支持 |
| **Quantumult X** | iOS | V2Ray | ✅ 完美支持 |

## 🛠️ 高级功能

### 隐藏管理面板

访问管理面板：
```
https://your-domain.com/phantom/your-phantom-id
```

管理面板功能：
- 📊 实时系统状态监控
- 🌐 IP池健康状况
- 🤖 AI学习模型状态
- 🔄 动态路径生成统计
- 👻 伪装引擎运行状态
- 📈 流量分析和优化建议

### API接口

```bash
# 获取系统状态
GET /api/v1/admin/status

# 获取IP池信息
GET /api/v1/admin/ips

# 强制刷新IP池
POST /api/v1/admin/refresh

# 获取AI学习状态
GET /api/v1/admin/ai-status
```

## 🔧 配置说明

### 伪装模式详解

| 模式 | 说明 | 特点 | 适用场景 |
|------|------|------|----------|
| `adaptive` | 自适应模式 | 智能选择最佳伪装 | 推荐使用 |
| `github` | GitHub克隆 | 完美模拟GitHub | 开发者用户 |
| `corporate` | 企业网站 | 模拟企业官网 | 工作时间 |
| `techblog` | 技术博客 | 模拟技术博客 | 技术用户 |

### AI行为模型

| 模型 | 请求间隔 | 突发模式 | 会话时长 | 特点 |
|------|----------|----------|----------|------|
| `human` | 1-5秒 | 30%概率 | 5-30分钟 | 模拟人类浏览 |
| `crawler` | 0.5-2秒 | 禁用 | 1-5分钟 | 模拟搜索爬虫 |
| `api` | 0.1-1秒 | 70%概率 | 1-10分钟 | 模拟API调用 |

### 路径复杂度

| 级别 | 路径长度 | 更新频率 | 安全性 | 性能影响 |
|------|----------|----------|--------|----------|
| `low` | 短路径 | 5分钟 | 基础 | 最小 |
| `medium` | 中等路径 | 2分钟 | 良好 | 轻微 |
| `high` | 长路径 | 1分钟 | 最高 | 轻微 |

## 🔍 故障排除

### 常见问题

#### 1. 无法获取订阅
**可能原因**：
- PHANTOM_ID未设置或错误
- 域名解析问题
- Workers服务未启动

**解决方法**：
1. 检查环境变量配置
2. 验证域名绑定状态
3. 查看Workers运行日志

#### 2. 连接不稳定
**可能原因**：
- IP质量问题
- 网络环境限制
- 客户端配置错误

**解决方法**：
1. 访问管理面板查看IP状态
2. 尝试不同的伪装模式
3. 检查客户端配置

#### 3. 速度较慢
**优化建议**：
1. 选择地理位置较近的IP
2. 调整AI行为模型为`api`模式
3. 降低路径复杂度为`medium`

### 调试模式

在开发环境中启用调试：
```javascript
// 在_worker.js中临时添加
PHANTOM_CONFIG.security.zeroLog = false;
```

**注意**：生产环境必须保持零日志模式！

## 🔒 安全建议

1. **定期更换ID**：建议每月更换PHANTOM_ID
2. **监控访问模式**：定期检查管理面板的访问统计
3. **使用自定义域名**：避免使用workers.dev域名
4. **启用全部安全特性**：确保所有安全配置正确启用
5. **保持更新**：及时关注项目更新

## 📊 性能优化

### 系统优化
- 利用Cloudflare全球边缘网络
- AI驱动的智能路由选择
- 动态负载均衡和故障转移
- 内存级缓存优化

### 网络优化
- 多IP池并行测试
- 实时网络质量监控
- 自适应连接超时
- 智能重连机制

## 🌟 技术特色

### 创新技术栈
- **Cloudflare Workers**: 边缘计算平台
- **WebAssembly**: 高性能加密计算
- **Machine Learning**: 行为模式学习
- **Quantum Cryptography**: 量子级安全通信

### 反检测技术
- **深度包检测规避**: 完全隐藏协议特征
- **流量指纹混淆**: 模拟真实应用流量
- **时序分析对抗**: 随机化通信模式
- **行为特征伪装**: AI驱动的用户行为模拟

## 📄 许可证

本项目基于 MIT 许可证开源。

## 🤝 贡献

欢迎提交Issue和Pull Request！

## ⚠️ 免责声明

本项目仅供学习和研究使用，请遵守当地法律法规。

---

**PhantomEdge Proxy** - 让网络访问真正隐形！ 👻✨
