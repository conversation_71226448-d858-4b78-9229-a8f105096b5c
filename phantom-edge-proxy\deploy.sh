#!/bin/bash

# PhantomEdge Proxy 部署脚本
# 自动化部署到Cloudflare Workers

set -e

echo "🚀 PhantomEdge Proxy 部署脚本"
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查依赖
check_dependencies() {
    echo -e "${BLUE}检查依赖...${NC}"
    
    if ! command -v node &> /dev/null; then
        echo -e "${RED}错误: Node.js 未安装${NC}"
        echo "请访问 https://nodejs.org 安装 Node.js"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}错误: npm 未安装${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✓ 依赖检查完成${NC}"
}

# 安装Wrangler
install_wrangler() {
    echo -e "${BLUE}安装/更新 Wrangler CLI...${NC}"
    
    if command -v wrangler &> /dev/null; then
        echo "Wrangler 已安装，检查更新..."
        npm update -g wrangler
    else
        echo "安装 Wrangler..."
        npm install -g wrangler
    fi
    
    echo -e "${GREEN}✓ Wrangler 安装完成${NC}"
}

# 登录Cloudflare
login_cloudflare() {
    echo -e "${BLUE}登录 Cloudflare...${NC}"
    
    if wrangler whoami &> /dev/null; then
        echo -e "${GREEN}✓ 已登录 Cloudflare${NC}"
        wrangler whoami
    else
        echo "请登录 Cloudflare 账户..."
        wrangler login
    fi
}

# 生成Phantom ID
generate_phantom_id() {
    if [ -z "$PHANTOM_ID" ]; then
        echo -e "${YELLOW}生成 Phantom ID...${NC}"
        PHANTOM_ID=$(openssl rand -hex 16)
        echo -e "${GREEN}✓ 生成的 Phantom ID: ${PHANTOM_ID}${NC}"
        echo -e "${YELLOW}请保存此ID，客户端配置时需要使用！${NC}"
    else
        echo -e "${GREEN}✓ 使用提供的 Phantom ID: ${PHANTOM_ID}${NC}"
    fi
}

# 设置环境变量
set_environment_variables() {
    echo -e "${BLUE}设置环境变量...${NC}"
    
    # 设置必需的环境变量
    wrangler secret put PHANTOM_ID --env production <<< "$PHANTOM_ID"
    
    # 设置可选环境变量
    if [ ! -z "$GHOST_MODE" ]; then
        wrangler secret put GHOST_MODE --env production <<< "$GHOST_MODE"
    fi
    
    if [ ! -z "$QUANTUM_ENCRYPTION" ]; then
        wrangler secret put QUANTUM_ENCRYPTION --env production <<< "$QUANTUM_ENCRYPTION"
    fi
    
    if [ ! -z "$AI_BEHAVIOR" ]; then
        wrangler secret put AI_BEHAVIOR --env production <<< "$AI_BEHAVIOR"
    fi
    
    if [ ! -z "$DYNAMIC_COMPLEXITY" ]; then
        wrangler secret put DYNAMIC_COMPLEXITY --env production <<< "$DYNAMIC_COMPLEXITY"
    fi
    
    echo -e "${GREEN}✓ 环境变量设置完成${NC}"
}

# 部署到Cloudflare Workers
deploy_worker() {
    echo -e "${BLUE}部署到 Cloudflare Workers...${NC}"
    
    # 部署到生产环境
    wrangler deploy --env production
    
    echo -e "${GREEN}✓ 部署完成${NC}"
}

# 获取部署信息
get_deployment_info() {
    echo -e "${BLUE}获取部署信息...${NC}"
    
    # 获取Worker URL
    WORKER_URL=$(wrangler subdomain get 2>/dev/null || echo "your-worker.your-subdomain.workers.dev")
    
    echo -e "${GREEN}================================${NC}"
    echo -e "${GREEN}🎉 PhantomEdge Proxy 部署成功！${NC}"
    echo -e "${GREEN}================================${NC}"
    echo ""
    echo -e "${YELLOW}📋 部署信息:${NC}"
    echo -e "Worker URL: https://${WORKER_URL}"
    echo -e "Phantom ID: ${PHANTOM_ID}"
    echo ""
    echo -e "${YELLOW}📱 订阅链接:${NC}"
    echo -e "通用订阅: https://${WORKER_URL}/sub/${PHANTOM_ID}"
    echo -e "Clash订阅: https://${WORKER_URL}/sub/${PHANTOM_ID}?clash"
    echo -e "V2Ray订阅: https://${WORKER_URL}/sub/${PHANTOM_ID}?v2ray"
    echo -e "Sing-Box订阅: https://${WORKER_URL}/sub/${PHANTOM_ID}?sing-box"
    echo ""
    echo -e "${YELLOW}🛠️ 管理面板:${NC}"
    echo -e "管理面板: https://${WORKER_URL}/phantom/${PHANTOM_ID}"
    echo ""
    echo -e "${YELLOW}⚠️ 重要提醒:${NC}"
    echo -e "1. 请保存好 Phantom ID，丢失后无法恢复"
    echo -e "2. 建议绑定自定义域名以提高稳定性"
    echo -e "3. 定期检查管理面板的运行状态"
    echo ""
}

# 主函数
main() {
    echo -e "${BLUE}开始部署 PhantomEdge Proxy...${NC}"
    echo ""
    
    # 检查是否在正确的目录
    if [ ! -f "_worker.js" ]; then
        echo -e "${RED}错误: 请在 phantom-edge-proxy 目录中运行此脚本${NC}"
        exit 1
    fi
    
    # 执行部署步骤
    check_dependencies
    install_wrangler
    login_cloudflare
    generate_phantom_id
    set_environment_variables
    deploy_worker
    get_deployment_info
    
    echo -e "${GREEN}🎉 部署完成！享受隐形的网络体验！${NC}"
}

# 帮助信息
show_help() {
    echo "PhantomEdge Proxy 部署脚本"
    echo ""
    echo "用法:"
    echo "  ./deploy.sh                    # 自动部署"
    echo "  ./deploy.sh --help            # 显示帮助"
    echo ""
    echo "环境变量:"
    echo "  PHANTOM_ID                    # 自定义Phantom ID"
    echo "  GHOST_MODE                    # 伪装模式 (adaptive/github/corporate/techblog)"
    echo "  QUANTUM_ENCRYPTION            # 加密算法"
    echo "  AI_BEHAVIOR                   # AI行为模型 (human/crawler/api)"
    echo "  DYNAMIC_COMPLEXITY            # 路径复杂度 (low/medium/high)"
    echo ""
    echo "示例:"
    echo "  PHANTOM_ID=my-custom-id ./deploy.sh"
    echo "  GHOST_MODE=github AI_BEHAVIOR=api ./deploy.sh"
}

# 处理命令行参数
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    *)
        main
        ;;
esac
