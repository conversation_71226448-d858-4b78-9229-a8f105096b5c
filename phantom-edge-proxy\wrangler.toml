name = "phantom-edge-proxy"
main = "_worker.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# 环境变量配置
[env.production.vars]
# 必需变量 - 部署时需要设置
# PHANTOM_ID = "your-unique-phantom-id"

# 可选变量 - 使用默认值
GHOST_MODE = "adaptive"
QUANTUM_ENCRYPTION = "AES-256-GCM"
AI_BEHAVIOR = "human"
DYNAMIC_COMPLEXITY = "high"

# 开发环境配置
[env.development.vars]
GHOST_MODE = "github"
AI_BEHAVIOR = "human"
DYNAMIC_COMPLEXITY = "medium"

# Workers配置
[build]
command = ""

# 资源限制
[limits]
cpu_ms = 50

# 触发器配置
[[triggers]]
patterns = ["*/*"]

# KV存储配置（如果需要持久化）
# [[kv_namespaces]]
# binding = "PHANTOM_KV"
# id = "your-kv-namespace-id"
# preview_id = "your-preview-kv-namespace-id"

# 自定义域名配置示例
# [[routes]]
# pattern = "phantom.yourdomain.com/*"
# zone_name = "yourdomain.com"
