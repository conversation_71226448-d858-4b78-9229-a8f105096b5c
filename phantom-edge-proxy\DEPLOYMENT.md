# 🚀 PhantomEdge Proxy 部署指南

## 快速部署（推荐）

### 方法一：一键脚本部署

```bash
# 1. 克隆项目
git clone <repository-url>
cd phantom-edge-proxy

# 2. 运行部署脚本
chmod +x deploy.sh
./deploy.sh
```

脚本会自动：
- ✅ 安装依赖
- ✅ 登录Cloudflare
- ✅ 生成Phantom ID
- ✅ 设置环境变量
- ✅ 部署Worker
- ✅ 显示订阅链接

### 方法二：手动部署

#### 1. 准备环境

```bash
# 安装Node.js和npm
# 访问 https://nodejs.org 下载安装

# 安装Wrangler CLI
npm install -g wrangler

# 登录Cloudflare
wrangler login
```

#### 2. 配置项目

```bash
# 克隆项目
git clone <repository-url>
cd phantom-edge-proxy

# 生成Phantom ID
PHANTOM_ID=$(openssl rand -hex 16)
echo "您的Phantom ID: $PHANTOM_ID"
```

#### 3. 设置环境变量

```bash
# 设置必需变量
wrangler secret put PHANTOM_ID
# 输入您的Phantom ID

# 设置可选变量（可跳过）
wrangler secret put GHOST_MODE
# 输入: adaptive

wrangler secret put AI_BEHAVIOR  
# 输入: human
```

#### 4. 部署

```bash
# 部署到Cloudflare Workers
wrangler deploy
```

## 环境变量详解

### 必需变量

| 变量名 | 说明 | 生成方法 |
|--------|------|----------|
| `PHANTOM_ID` | 系统唯一标识符 | `openssl rand -hex 16` |

### 可选变量

| 变量名 | 默认值 | 可选值 | 说明 |
|--------|--------|--------|------|
| `GHOST_MODE` | `adaptive` | `adaptive`, `github`, `corporate`, `techblog` | 伪装模式 |
| `QUANTUM_ENCRYPTION` | `AES-256-GCM` | `AES-256-GCM`, `ChaCha20-Poly1305` | 加密算法 |
| `AI_BEHAVIOR` | `human` | `human`, `crawler`, `api` | AI行为模型 |
| `DYNAMIC_COMPLEXITY` | `high` | `low`, `medium`, `high` | 路径复杂度 |

## 自定义域名配置

### 1. 添加域名到Cloudflare

1. 登录Cloudflare Dashboard
2. 点击 "Add a Site"
3. 输入您的域名
4. 选择免费计划
5. 更新域名DNS服务器

### 2. 绑定Worker到域名

```bash
# 方法一：使用Wrangler
wrangler route add "phantom.yourdomain.com/*" phantom-edge-proxy

# 方法二：在Dashboard中配置
# 1. 进入Workers & Pages
# 2. 选择您的Worker
# 3. 点击 "Triggers" 标签
# 4. 点击 "Add Custom Domain"
# 5. 输入子域名：phantom.yourdomain.com
```

### 3. 更新wrangler.toml（可选）

```toml
[[routes]]
pattern = "phantom.yourdomain.com/*"
zone_name = "yourdomain.com"
```

## 验证部署

### 1. 检查Worker状态

```bash
# 查看Worker信息
wrangler status

# 查看日志
wrangler tail
```

### 2. 测试伪装页面

访问您的Worker URL，应该看到一个完美的伪装网站。

### 3. 测试订阅链接

```bash
# 测试订阅链接
curl "https://your-worker-url/sub/your-phantom-id"
```

### 4. 访问管理面板

```
https://your-worker-url/phantom/your-phantom-id
```

## 客户端配置

### Clash Verge配置

1. 打开Clash Verge
2. 点击 "订阅" → "新建"
3. 输入订阅链接：
   ```
   https://your-domain/sub/your-phantom-id?clash
   ```
4. 点击 "保存并更新"

### V2RayN配置

1. 打开V2RayN
2. 点击 "订阅" → "订阅设置"
3. 点击 "添加"
4. 输入订阅链接：
   ```
   https://your-domain/sub/your-phantom-id?v2ray
   ```
5. 点击 "确定" → "更新订阅"

### 移动端配置

#### Android (V2RayNG)
1. 打开V2RayNG
2. 点击右上角 "+"
3. 选择 "从剪贴板导入"
4. 或手动添加订阅链接

#### iOS (Shadowrocket)
1. 打开Shadowrocket
2. 点击右上角 "+"
3. 选择 "Subscribe"
4. 输入订阅链接

## 故障排除

### 常见问题

#### 1. 部署失败
```bash
# 检查登录状态
wrangler whoami

# 重新登录
wrangler logout
wrangler login
```

#### 2. 环境变量未生效
```bash
# 查看环境变量
wrangler secret list

# 重新设置
wrangler secret put PHANTOM_ID
```

#### 3. 订阅链接无法访问
- 检查PHANTOM_ID是否正确设置
- 确认Worker已成功部署
- 验证域名解析是否正常

#### 4. 连接不稳定
- 访问管理面板查看IP状态
- 尝试更换伪装模式
- 检查网络环境

### 调试命令

```bash
# 查看Worker日志
wrangler tail

# 查看部署状态
wrangler status

# 测试本地开发
wrangler dev

# 查看路由配置
wrangler route list
```

## 安全建议

### 1. 定期维护
- 每月更换一次PHANTOM_ID
- 定期检查管理面板状态
- 关注项目更新

### 2. 域名安全
- 使用自定义域名
- 启用Cloudflare安全功能
- 定期检查DNS配置

### 3. 访问控制
- 不要分享管理面板链接
- 定期检查访问日志
- 使用强密码保护Cloudflare账户

## 性能优化

### 1. 地理位置优化
```bash
# 设置首选地区
wrangler secret put PREFERRED_REGIONS
# 输入: US,SG,JP
```

### 2. 缓存优化
- 启用Cloudflare缓存
- 配置适当的TTL
- 使用边缘缓存

### 3. 监控优化
- 定期查看管理面板
- 监控IP池健康状况
- 调整AI行为模型

## 更新升级

### 1. 更新代码
```bash
# 拉取最新代码
git pull origin main

# 重新部署
wrangler deploy
```

### 2. 更新配置
```bash
# 更新环境变量
wrangler secret put VARIABLE_NAME

# 重启Worker
wrangler deploy
```

### 3. 备份恢复
```bash
# 备份配置
wrangler secret list > backup.txt

# 恢复配置
# 手动重新设置环境变量
```

---

🎉 **部署完成后，您就拥有了一个完全隐形的代理系统！**

如有问题，请查看项目文档或提交Issue。
