/**
 * PhantomEdge Proxy 测试脚本
 * 用于验证系统功能和反检测能力
 */

// 测试配置
const TEST_CONFIG = {
    workerUrl: 'https://your-worker-url.workers.dev',
    phantomId: 'your-phantom-id',
    testTimeout: 30000
};

/**
 * 测试伪装页面
 */
async function testCamouflage() {
    console.log('🔮 测试伪装页面...');
    
    try {
        const response = await fetch(TEST_CONFIG.workerUrl, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        });
        
        const content = await response.text();
        
        // 检查伪装页面特征
        const checks = [
            { name: 'HTML结构完整', test: content.includes('<!DOCTYPE html>') },
            { name: '包含CSS样式', test: content.includes('<style>') || content.includes('.css') },
            { name: '包含JavaScript', test: content.includes('<script>') },
            { name: '包含SEO元素', test: content.includes('<meta name="description"') },
            { name: '响应状态正常', test: response.status === 200 },
            { name: '内容类型正确', test: response.headers.get('content-type').includes('text/html') }
        ];
        
        console.log('伪装页面检查结果:');
        checks.forEach(check => {
            console.log(`  ${check.test ? '✅' : '❌'} ${check.name}`);
        });
        
        return checks.every(check => check.test);
        
    } catch (error) {
        console.error('❌ 伪装页面测试失败:', error.message);
        return false;
    }
}

/**
 * 测试订阅生成
 */
async function testSubscription() {
    console.log('📱 测试订阅生成...');
    
    const subscriptionTypes = ['clash', 'v2ray', 'sing-box'];
    const results = [];
    
    for (const type of subscriptionTypes) {
        try {
            const url = `${TEST_CONFIG.workerUrl}/sub/${TEST_CONFIG.phantomId}?${type}`;
            const response = await fetch(url);
            
            const success = response.status === 200;
            console.log(`  ${success ? '✅' : '❌'} ${type.toUpperCase()} 订阅`);
            
            if (success) {
                const content = await response.text();
                console.log(`    内容长度: ${content.length} 字符`);
            }
            
            results.push(success);
            
        } catch (error) {
            console.error(`  ❌ ${type.toUpperCase()} 订阅测试失败:`, error.message);
            results.push(false);
        }
    }
    
    return results.every(result => result);
}

/**
 * 测试管理面板
 */
async function testAdminPanel() {
    console.log('🛠️ 测试管理面板...');
    
    try {
        const url = `${TEST_CONFIG.workerUrl}/phantom/${TEST_CONFIG.phantomId}`;
        const response = await fetch(url);
        
        const success = response.status === 200;
        console.log(`  ${success ? '✅' : '❌'} 管理面板访问`);
        
        if (success) {
            const content = await response.text();
            const hasAdminFeatures = [
                content.includes('系统状态'),
                content.includes('性能指标'),
                content.includes('IP池状态')
            ].every(check => check);
            
            console.log(`  ${hasAdminFeatures ? '✅' : '❌'} 管理功能完整`);
            return hasAdminFeatures;
        }
        
        return false;
        
    } catch (error) {
        console.error('❌ 管理面板测试失败:', error.message);
        return false;
    }
}

/**
 * 测试动态路径生成
 */
async function testDynamicPaths() {
    console.log('🌊 测试动态路径生成...');
    
    const paths = new Set();
    const testCount = 5;
    
    for (let i = 0; i < testCount; i++) {
        try {
            // 模拟不同的用户请求
            const userAgent = `TestAgent-${i}`;
            const response = await fetch(TEST_CONFIG.workerUrl, {
                headers: { 'User-Agent': userAgent }
            });
            
            // 检查响应中是否包含动态路径信息
            const content = await response.text();
            
            // 简单的路径唯一性检查（实际实现中路径会更复杂）
            const pathMatch = content.match(/\/[a-z0-9]{8,}/g);
            if (pathMatch) {
                pathMatch.forEach(path => paths.add(path));
            }
            
        } catch (error) {
            console.error(`路径测试 ${i + 1} 失败:`, error.message);
        }
    }
    
    const uniquePaths = paths.size > 0;
    console.log(`  ${uniquePaths ? '✅' : '❌'} 动态路径生成 (发现 ${paths.size} 个唯一路径)`);
    
    return uniquePaths;
}

/**
 * 测试反检测能力
 */
async function testAntiDetection() {
    console.log('👻 测试反检测能力...');
    
    const detectionTests = [
        {
            name: '无VLESS特征',
            test: async () => {
                const response = await fetch(TEST_CONFIG.workerUrl);
                const content = await response.text();
                return !content.toLowerCase().includes('vless');
            }
        },
        {
            name: '无代理关键词',
            test: async () => {
                const response = await fetch(TEST_CONFIG.workerUrl);
                const content = await response.text();
                const proxyKeywords = ['proxy', 'tunnel', 'vpn', 'shadowsocks'];
                return !proxyKeywords.some(keyword => 
                    content.toLowerCase().includes(keyword)
                );
            }
        },
        {
            name: '真实HTTP头',
            test: async () => {
                const response = await fetch(TEST_CONFIG.workerUrl);
                const headers = response.headers;
                return headers.has('content-type') && 
                       headers.has('cache-control') &&
                       !headers.has('x-proxy-by');
            }
        },
        {
            name: '正常响应时间',
            test: async () => {
                const start = Date.now();
                await fetch(TEST_CONFIG.workerUrl);
                const duration = Date.now() - start;
                return duration < 5000; // 5秒内响应
            }
        }
    ];
    
    const results = [];
    
    for (const test of detectionTests) {
        try {
            const result = await test.test();
            console.log(`  ${result ? '✅' : '❌'} ${test.name}`);
            results.push(result);
        } catch (error) {
            console.error(`  ❌ ${test.name} 测试失败:`, error.message);
            results.push(false);
        }
    }
    
    return results.every(result => result);
}

/**
 * 性能基准测试
 */
async function performanceBenchmark() {
    console.log('⚡ 性能基准测试...');
    
    const testCount = 10;
    const times = [];
    
    for (let i = 0; i < testCount; i++) {
        try {
            const start = Date.now();
            const response = await fetch(TEST_CONFIG.workerUrl);
            await response.text();
            const duration = Date.now() - start;
            times.push(duration);
        } catch (error) {
            console.error(`性能测试 ${i + 1} 失败:`, error.message);
        }
    }
    
    if (times.length > 0) {
        const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
        const minTime = Math.min(...times);
        const maxTime = Math.max(...times);
        
        console.log(`  平均响应时间: ${avgTime.toFixed(2)}ms`);
        console.log(`  最快响应时间: ${minTime}ms`);
        console.log(`  最慢响应时间: ${maxTime}ms`);
        
        return avgTime < 2000; // 平均响应时间小于2秒
    }
    
    return false;
}

/**
 * 主测试函数
 */
async function runTests() {
    console.log('🚀 PhantomEdge Proxy 系统测试开始');
    console.log('=====================================');
    
    // 检查配置
    if (!TEST_CONFIG.workerUrl || !TEST_CONFIG.phantomId) {
        console.error('❌ 请先配置 TEST_CONFIG 中的 workerUrl 和 phantomId');
        return;
    }
    
    const tests = [
        { name: '伪装页面', fn: testCamouflage },
        { name: '订阅生成', fn: testSubscription },
        { name: '管理面板', fn: testAdminPanel },
        { name: '动态路径', fn: testDynamicPaths },
        { name: '反检测能力', fn: testAntiDetection },
        { name: '性能基准', fn: performanceBenchmark }
    ];
    
    const results = [];
    
    for (const test of tests) {
        console.log(`\n🧪 ${test.name}测试:`);
        try {
            const result = await Promise.race([
                test.fn(),
                new Promise((_, reject) => 
                    setTimeout(() => reject(new Error('测试超时')), TEST_CONFIG.testTimeout)
                )
            ]);
            results.push({ name: test.name, success: result });
        } catch (error) {
            console.error(`❌ ${test.name}测试失败:`, error.message);
            results.push({ name: test.name, success: false });
        }
    }
    
    // 输出测试总结
    console.log('\n📊 测试总结:');
    console.log('=============');
    
    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;
    
    results.forEach(result => {
        console.log(`${result.success ? '✅' : '❌'} ${result.name}`);
    });
    
    console.log(`\n总体结果: ${successCount}/${totalCount} 测试通过`);
    
    if (successCount === totalCount) {
        console.log('🎉 所有测试通过！PhantomEdge Proxy 运行正常！');
    } else {
        console.log('⚠️ 部分测试失败，请检查系统配置和部署状态。');
    }
}

// 如果直接运行此脚本
if (typeof window === 'undefined') {
    // Node.js 环境
    runTests().catch(console.error);
} else {
    // 浏览器环境
    window.runPhantomTests = runTests;
    console.log('测试函数已加载，请调用 runPhantomTests() 开始测试');
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        runTests,
        testCamouflage,
        testSubscription,
        testAdminPanel,
        testDynamicPaths,
        testAntiDetection,
        performanceBenchmark
    };
}
